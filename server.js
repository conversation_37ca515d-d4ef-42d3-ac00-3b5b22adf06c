const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const db = require('./models');
const routes = require('./routes');
const { errorConverter, errorHandler } = require("./middleware/error.middleware");

// 📦 Load environment variables from .env
dotenv.config();

// 🚀 Initialize app
const app = express();

// 🛡️ Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add request logging middleware in development
if (process.env.NODE_ENV !== 'production') {
    app.use((req, res, next) => {
        console.log(`${req.method} ${req.url}`);
        next();
    });
}

// 🔗 Routes
app.use('/', routes);

// Error handling middleware (should be after routes)
app.use(errorConverter);
app.use(errorHandler);

// ⚙️ Connect to DB and start server
const PORT = process.env.PORT || 3000;

// Test database connection first
db.sequelize.sync()
    .then(() => {
        console.log('✅ Database connection established successfully');

        // Use force: false and alter: false to avoid index conflicts
        // For production, use migrations instead of sync
        return db.sequelize.sync({ force: false, alter: false });
    })
    .then(() => {
        // console.log('✅ Database synchronized');

        app.listen(PORT, () => {
            console.log(`🚀 Server running on http://localhost:${PORT}`);
        });
    })
    .catch(err => {
        console.error('❌ Failed to connect to DB:', err.message);
        console.error('Full error:', err);
        process.exit(1);
    });
