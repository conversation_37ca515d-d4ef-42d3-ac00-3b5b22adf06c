const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const db = require('./models');
const routes = require('./routes');
const { errorConverter, errorHandler } = require("./middleware/error.middleware");
const logger = require('./config/logger');

// 📦 Load environment variables from .env
dotenv.config();

// 🚀 Initialize app
const app = express();

// 🛡️ Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add request logging middleware
app.use((req, res, next) => {
    logger.http(`${req.method} ${req.url} - ${req.ip}`);
    next();
});

// 🔗 Routes
app.use('/', routes);

// Error handling middleware (should be after routes)
app.use(errorConverter);
app.use(errorHandler);

// ⚙️ Connect to DB and start server
const PORT = process.env.PORT || 3000;

// Test database connection first
db.sequelize.sync()
    .then(() => {
        logger.info('✅ Database connection established successfully');

        // Use force: false and alter: false to avoid index conflicts
        // For production, use migrations instead of sync
        return db.sequelize.sync({ force: false, alter: false });
    })
    .then(() => {
        logger.info('✅ Database synchronized');

        app.listen(PORT, () => {
            logger.info(`🚀 Server running on http://localhost:${PORT}`);
        });
    })
    .catch(err => {
        logger.error('❌ Failed to connect to DB:', err.message);
        logger.error('Full error:', err);
        process.exit(1);
    });
