# Logging System Documentation

This project uses <PERSON> for comprehensive logging throughout the application.

## Log Levels

- **error**: Error events that might still allow the application to continue running
- **warn**: Warning events that indicate potential issues
- **info**: General information about application flow
- **http**: HTTP request/response logging
- **debug**: Detailed information for debugging (only in development)

## Log Output

### Console Output
- Colored output for easy reading during development
- Includes timestamp, level, and message

### File Output
- `logs/error.log`: Contains only error-level logs
- `logs/combined.log`: Contains all log levels
- JSON format for easy parsing and analysis

## Usage Examples

### Basic Logging
```javascript
const logger = require('../config/logger');

// Different log levels
logger.error('Something went wrong', error);
logger.warn('This is a warning message');
logger.info('Application started successfully');
logger.http('GET /api/users - 200');
logger.debug('Debug information');
```

### Using Logger Helper
```javascript
const LoggerHelper = require('../helpers/logger.helper');

// API request logging
LoggerHelper.logApiRequest('GET', '/api/users', userId, 'fetching user list');
LoggerHelper.logApiSuccess('POST', '/api/users', userId, 'user created');
LoggerHelper.logValidationError('POST', '/api/users', userId, 'Invalid email format');

// Authentication events
LoggerHelper.logAuthEvent('LOGIN', username, userId, true);
LoggerHelper.logAuthEvent('LOGIN_FAILED', username, null, false);

// Database operations
LoggerHelper.logDatabaseOperation('CREATE', 'users', newUserId, userId);
LoggerHelper.logDatabaseOperation('UPDATE', 'users', userId, userId);

// Business events
LoggerHelper.logBusinessEvent('Investment created', userId, { amount: 1000, plan: 'gold' });

// Security events
LoggerHelper.logSecurityEvent('Multiple failed login attempts', req.ip, req.get('User-Agent'), userId);
```

## Log Format

### Console Format
```
2024-01-15 10:30:45:123 info: ✅ User logged in successfully: john_doe (ID: 123)
2024-01-15 10:30:46:456 warn: ❌ Login failed for user: invalid_user - Invalid credentials
2024-01-15 10:30:47:789 error: Registration transaction error: [Error details]
```

### File Format (JSON)
```json
{
  "timestamp": "2024-01-15 10:30:45:123",
  "level": "info",
  "message": "✅ User logged in successfully: john_doe (ID: 123)"
}
```

## Environment Configuration

The logging level is automatically set based on the environment:
- **Development**: `debug` level (shows all logs)
- **Production**: `warn` level (shows only warnings and errors)

## Log File Management

- Log files are automatically created in the `logs/` directory
- The directory is tracked by git but log files are ignored
- Consider implementing log rotation for production environments

## Best Practices

1. **Use appropriate log levels**: Don't use `error` for validation failures, use `warn` instead
2. **Include context**: Always include user IDs, request IDs, or other relevant context
3. **Structured logging**: Use the LoggerHelper for consistent log formatting
4. **Sensitive data**: Never log passwords, tokens, or other sensitive information
5. **Performance**: Avoid excessive logging in production, especially debug logs

## Integration Examples

The logging system is already integrated into:
- Server startup and database connection
- Authentication controllers
- Error middleware
- HTTP request middleware

To add logging to new controllers or services, simply import the logger and use it as shown in the examples above.
