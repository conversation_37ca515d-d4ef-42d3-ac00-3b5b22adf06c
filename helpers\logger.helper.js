const logger = require('../config/logger');

/**
 * Logger helper functions for common logging patterns
 */
class LoggerHelper {
    /**
     * Log API request start
     * @param {string} method - HTTP method
     * @param {string} url - Request URL
     * @param {string} userId - User ID (optional)
     * @param {string} action - Action description
     */
    static logApiRequest(method, url, userId = null, action = '') {
        const userInfo = userId ? `User ${userId}` : 'Anonymous user';
        logger.info(`${method} ${url} - ${userInfo} ${action}`);
    }

    /**
     * Log successful API response
     * @param {string} method - HTTP method
     * @param {string} url - Request URL
     * @param {string} userId - User ID (optional)
     * @param {string} action - Action description
     */
    static logApiSuccess(method, url, userId = null, action = '') {
        const userInfo = userId ? `User ${userId}` : 'Anonymous user';
        logger.info(`✅ ${method} ${url} - ${userInfo} ${action} - SUCCESS`);
    }

    /**
     * Log API validation error
     * @param {string} method - HTTP method
     * @param {string} url - Request URL
     * @param {string} userId - User ID (optional)
     * @param {string} error - Error message
     */
    static logValidationError(method, url, userId = null, error = '') {
        const userInfo = userId ? `User ${userId}` : 'Anonymous user';
        logger.warn(`❌ ${method} ${url} - ${userInfo} - VALIDATION ERROR: ${error}`);
    }

    /**
     * Log database operation
     * @param {string} operation - Database operation (CREATE, UPDATE, DELETE, etc.)
     * @param {string} table - Table name
     * @param {string} recordId - Record ID (optional)
     * @param {string} userId - User ID (optional)
     */
    static logDatabaseOperation(operation, table, recordId = null, userId = null) {
        const recordInfo = recordId ? `ID: ${recordId}` : '';
        const userInfo = userId ? `by User ${userId}` : '';
        logger.info(`🗄️  ${operation} ${table} ${recordInfo} ${userInfo}`);
    }

    /**
     * Log authentication events
     * @param {string} event - Auth event (LOGIN, LOGOUT, REGISTER, etc.)
     * @param {string} username - Username
     * @param {string} userId - User ID (optional)
     * @param {boolean} success - Whether the event was successful
     */
    static logAuthEvent(event, username, userId = null, success = true) {
        const status = success ? '✅' : '❌';
        const userInfo = userId ? `(ID: ${userId})` : '';
        logger.info(`🔐 ${status} ${event} - ${username} ${userInfo}`);
    }

    /**
     * Log business logic events
     * @param {string} event - Business event description
     * @param {string} userId - User ID (optional)
     * @param {object} data - Additional data (optional)
     */
    static logBusinessEvent(event, userId = null, data = null) {
        const userInfo = userId ? `User ${userId}` : '';
        const dataInfo = data ? `Data: ${JSON.stringify(data)}` : '';
        logger.info(`💼 ${event} - ${userInfo} ${dataInfo}`);
    }

    /**
     * Log security events
     * @param {string} event - Security event description
     * @param {string} ip - IP address
     * @param {string} userAgent - User agent (optional)
     * @param {string} userId - User ID (optional)
     */
    static logSecurityEvent(event, ip, userAgent = null, userId = null) {
        const userInfo = userId ? `User ${userId}` : 'Anonymous';
        const agentInfo = userAgent ? `Agent: ${userAgent}` : '';
        logger.warn(`🔒 SECURITY: ${event} - ${userInfo} - IP: ${ip} ${agentInfo}`);
    }
}

module.exports = LoggerHelper;
