const {
    ValidationError,
    DatabaseError,
    ConnectionError,
} = require("sequelize");
const { ApiError } = require("../helpers/api.helper");

// Error Response Function
const sendError = (res, message, code = 400, data = []) => {
    res.status(code).send({
        status: false,
        message,
        data,
    });
};

const errorConverter = (err, req, res, next) => {
    let error = err;
    if (!(error instanceof ApiError)) {
        let statusCode = 500;
        let message = "Internal Server Error";

        if (error instanceof ValidationError) {
            statusCode = 400;
            message = error.message;
        } else if (error instanceof DatabaseError) {
            statusCode = 500;
            message = error.message;
        } else if (error instanceof ConnectionError) {
            statusCode = 500;
            message = "Database connection error";
        } else if (error.statusCode) {
            statusCode = error.statusCode;
            message = error.message || "Internal Server Error";
        }

        error = new ApiError(statusCode, message, false, err.stack);
    }
    next(error);
};

// eslint-disable-next-line no-unused-vars
const errorHandler = (err, req, res, next) => {
    let { statusCode, message } = err;
    const isProduction = process.env.NODE_ENV === "production";

    if (isProduction && !err.isOperational) {
        statusCode = 500;
        message = "Internal Server Error";
    }

    res.locals.errorMessage = err.message;

    let data = [];
    if (!isProduction) {
        console.error(err);
        data = err.stack;
    }

    return sendError(res, message, statusCode, data);
};

module.exports = {
    errorConverter,
    errorHandler,
};
