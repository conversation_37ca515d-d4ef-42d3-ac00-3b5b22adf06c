/**
 * Logging Examples for Gold Project
 * 
 * This file shows how to use the logging system in different scenarios
 */

const logger = require('../config/logger');
const LoggerHelper = require('../helpers/logger.helper');

// ===== BASIC LOGGING EXAMPLES =====

// Simple info logging
logger.info('Application started successfully');

// Warning logging
logger.warn('User attempted to access restricted resource');

// Error logging with error object
try {
    // Some operation that might fail
    throw new Error('Database connection failed');
} catch (error) {
    logger.error('Database operation failed:', error);
}

// HTTP request logging
logger.http('GET /api/users - 200 - 45ms');

// Debug logging (only shows in development)
logger.debug('User session data:', { userId: 123, sessionId: 'abc123' });

// ===== LOGGER HELPER EXAMPLES =====

// API Request Logging
LoggerHelper.logApiRequest('POST', '/api/auth/login', null, 'attempting login');
LoggerHelper.logApiSuccess('POST', '/api/auth/login', 123, 'login successful');
LoggerHelper.logValidationError('POST', '/api/auth/register', null, 'Email format invalid');

// Authentication Events
LoggerHelper.logAuthEvent('LOGIN', 'john_doe', 123, true);
LoggerHelper.logAuthEvent('LOGIN_FAILED', 'invalid_user', null, false);
LoggerHelper.logAuthEvent('REGISTER', 'new_user', 456, true);
LoggerHelper.logAuthEvent('LOGOUT', 'john_doe', 123, true);

// Database Operations
LoggerHelper.logDatabaseOperation('CREATE', 'users', 789, 123);
LoggerHelper.logDatabaseOperation('UPDATE', 'users', 123, 123);
LoggerHelper.logDatabaseOperation('DELETE', 'investments', 456, 123);

// Business Events
LoggerHelper.logBusinessEvent('Investment created', 123, { 
    amount: 1000, 
    plan: 'gold', 
    duration: '12 months' 
});

LoggerHelper.logBusinessEvent('Bonus awarded', 123, { 
    type: 'referral', 
    amount: 50 
});

LoggerHelper.logBusinessEvent('Withdrawal requested', 123, { 
    amount: 500, 
    method: 'bank_transfer' 
});

// Security Events
LoggerHelper.logSecurityEvent(
    'Multiple failed login attempts', 
    '*************', 
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 
    null
);

LoggerHelper.logSecurityEvent(
    'Suspicious activity detected', 
    '********', 
    'curl/7.68.0', 
    123
);

// ===== CONTROLLER INTEGRATION EXAMPLES =====

// Example: User Registration Controller
const exampleRegisterController = async (req, res) => {
    try {
        const { user_name, email } = req.body;
        
        // Log the registration attempt
        LoggerHelper.logApiRequest(req.method, req.url, null, `registration attempt for ${user_name}`);
        
        // Validation logging
        if (!email || !email.includes('@')) {
            LoggerHelper.logValidationError(req.method, req.url, null, 'Invalid email format');
            return res.status(400).json({ error: 'Invalid email' });
        }
        
        // Database operation logging
        const newUser = { user_id: 123, user_name, email };
        LoggerHelper.logDatabaseOperation('CREATE', 'users', newUser.user_id, null);
        
        // Authentication event logging
        LoggerHelper.logAuthEvent('REGISTER', user_name, newUser.user_id, true);
        
        // Success logging
        LoggerHelper.logApiSuccess(req.method, req.url, newUser.user_id, 'registration completed');
        
        return res.json({ success: true, user: newUser });
        
    } catch (error) {
        logger.error('Registration controller error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
};

// Example: Investment Controller
const exampleInvestmentController = async (req, res) => {
    try {
        const userId = req.user.id;
        const { amount, plan_id } = req.body;
        
        LoggerHelper.logApiRequest(req.method, req.url, userId, `creating investment of ${amount}`);
        
        // Business logic logging
        LoggerHelper.logBusinessEvent('Investment validation started', userId, { amount, plan_id });
        
        // Database operations
        const investment = { id: 456, amount, plan_id, user_id: userId };
        LoggerHelper.logDatabaseOperation('CREATE', 'investments', investment.id, userId);
        
        // Business event
        LoggerHelper.logBusinessEvent('Investment created successfully', userId, {
            investment_id: investment.id,
            amount,
            plan_id
        });
        
        LoggerHelper.logApiSuccess(req.method, req.url, userId, 'investment created');
        
        return res.json({ success: true, investment });
        
    } catch (error) {
        logger.error('Investment controller error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
};

// ===== ERROR HANDLING EXAMPLES =====

// Structured error logging
const logStructuredError = (error, context = {}) => {
    logger.error('Structured error occurred', {
        message: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString()
    });
};

// Usage example
try {
    // Some operation
    throw new Error('Payment processing failed');
} catch (error) {
    logStructuredError(error, {
        userId: 123,
        action: 'process_payment',
        amount: 1000,
        paymentMethod: 'credit_card'
    });
}

console.log('Logging examples completed. Check the logs/ directory for output files.');
