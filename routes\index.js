const express = require('express');
const router = express.Router();

// Import all route modules
const authRoutes = require('./auth.route');
const userRoutes = require('./user.routes');
const adminRoutes = require('./admin.routes');
const planRoutes = require('./plan.routes');
const investmentRoutes = require('./investment.routes');
const bonusRoutes = require('./bonus.routes');
const passwordResetRoutes = require('./passwordReset.routes');

// Define all routes
router.use('/api', authRoutes);
router.use('/api/user', userRoutes);
router.use('/api/admin', adminRoutes);
router.use('/api/plans', planRoutes);
router.use('/api/investments', investmentRoutes);
router.use('/api/bonuses', bonusRoutes);
router.use('/', passwordResetRoutes);

// Default route
router.get('/', (req, res) => {
    res.send('Welcome to gold trading platform!');
});

module.exports = router;